[{"/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx": "1", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx": "2", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx": "3", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx": "4", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx": "7", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx": "8", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts": "9"}, {"size": 272, "mtime": *************, "results": "10", "hashOfConfig": "11"}, {"size": 1035, "mtime": *************, "results": "12", "hashOfConfig": "11"}, {"size": 2045, "mtime": *************, "results": "13", "hashOfConfig": "11"}, {"size": 646, "mtime": *************, "results": "14", "hashOfConfig": "11"}, {"size": 1976, "mtime": *************, "results": "15", "hashOfConfig": "11"}, {"size": 5067, "mtime": *************, "results": "16", "hashOfConfig": "11"}, {"size": 6154, "mtime": *************, "results": "17", "hashOfConfig": "11"}, {"size": 2050, "mtime": *************, "results": "18", "hashOfConfig": "11"}, {"size": 721, "mtime": *************, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sxxfvo", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts", ["47"], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 2, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 2, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'message' is defined but never used.", "Identifier", "unusedVar"]