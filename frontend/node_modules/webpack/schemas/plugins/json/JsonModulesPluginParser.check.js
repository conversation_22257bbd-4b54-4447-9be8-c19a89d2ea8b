/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn fix:special` to update
 */
"use strict";function r(e,{instancePath:t="",parentData:o,parentDataProperty:a,rootData:s=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return r.errors=[{params:{type:"object"}}],!1;{const t=0;for(const t in e)if("exportsDepth"!==t&&"parse"!==t)return r.errors=[{params:{additionalProperty:t}}],!1;if(0===t){if(void 0!==e.exportsDepth){const t=0;if("number"!=typeof e.exportsDepth)return r.errors=[{params:{type:"number"}}],!1;var n=0===t}else n=!0;if(n)if(void 0!==e.parse){const t=0;if(!(e.parse instanceof Function))return r.errors=[{params:{}}],!1;n=0===t}else n=!0}}return r.errors=null,!0}module.exports=r,module.exports.default=r;