/**
 * Feature:
 *  - fixed not need to set width
 *  - support `rowExpandable` to config row expand logic
 *  - add `summary` to support `() => ReactNode`
 *
 * Update:
 *  - `dataIndex` is `array[]` now
 *  - `expandable` wrap all the expand related props
 *
 * Removed:
 *  - expandIconAsCell
 *  - useFixedHeader
 *  - rowRef
 *  - columns[number].onCellClick
 *  - onRowClick
 *  - onRowDoubleClick
 *  - onRowMouseEnter
 *  - onRowMouseLeave
 *  - getBodyWrapper
 *  - bodyStyle
 *
 * Deprecated:
 *  - All expanded props, move into expandable
 */
import type { CompareProps } from '@rc-component/context/lib/Immutable';
import * as React from 'react';
import { EXPAND_COLUMN, INTERNAL_HOOKS } from './constant';
import { FooterComponents } from './Footer';
import type { ColumnsType, ColumnType, DefaultRecordType, Direction, ExpandableConfig, GetComponentProps, GetRowKey, LegacyExpandableProps, PanelRender, Reference, RowClassName, TableComponents, TableLayout, TableSticky } from './interface';
import Column from './sugar/Column';
import ColumnGroup from './sugar/ColumnGroup';
export declare const DEFAULT_PREFIX = "rc-table";
export interface TableProps<RecordType = any> extends Omit<LegacyExpandableProps<RecordType>, 'showExpandColumn'> {
    prefixCls?: string;
    className?: string;
    style?: React.CSSProperties;
    children?: React.ReactNode;
    data?: readonly RecordType[];
    columns?: ColumnsType<RecordType>;
    rowKey?: string | keyof RecordType | GetRowKey<RecordType>;
    tableLayout?: TableLayout;
    scroll?: {
        x?: number | true | string;
        y?: number | string;
    };
    /** Config expand rows */
    expandable?: ExpandableConfig<RecordType>;
    indentSize?: number;
    rowClassName?: string | RowClassName<RecordType>;
    footer?: PanelRender<RecordType>;
    summary?: (data: readonly RecordType[]) => React.ReactNode;
    caption?: React.ReactNode;
    id?: string;
    showHeader?: boolean;
    components?: TableComponents<RecordType>;
    onRow?: GetComponentProps<RecordType>;
    onHeaderRow?: GetComponentProps<readonly ColumnType<RecordType>[]>;
    emptyText?: React.ReactNode | (() => React.ReactNode);
    direction?: Direction;
    sticky?: boolean | TableSticky;
    rowHoverable?: boolean;
    onScroll?: React.UIEventHandler<HTMLDivElement>;
    /**
     * @private Internal usage, may remove by refactor. Should always use `columns` instead.
     *
     * !!! DO NOT USE IN PRODUCTION ENVIRONMENT !!!
     */
    internalHooks?: string;
    /**
     * @private Internal usage, may remove by refactor. Should always use `columns` instead.
     *
     * !!! DO NOT USE IN PRODUCTION ENVIRONMENT !!!
     */
    transformColumns?: (columns: ColumnsType<RecordType>) => ColumnsType<RecordType>;
    /**
     * @private Internal usage, may remove by refactor.
     *
     * !!! DO NOT USE IN PRODUCTION ENVIRONMENT !!!
     */
    tailor?: boolean;
    /**
     * @private Internal usage, may remove by refactor.
     *
     * !!! DO NOT USE IN PRODUCTION ENVIRONMENT !!!
     */
    getContainerWidth?: (ele: HTMLElement, width: number) => number;
    /**
     * @private Internal usage, may remove by refactor.
     *
     * !!! DO NOT USE IN PRODUCTION ENVIRONMENT !!!
     */
    internalRefs?: {
        body: React.MutableRefObject<HTMLDivElement>;
    };
}
declare function Table<RecordType extends DefaultRecordType>(tableProps: TableProps<RecordType>, ref: React.Ref<Reference>): React.JSX.Element;
export type ForwardGenericTable = (<RecordType extends DefaultRecordType = any>(props: TableProps<RecordType> & React.RefAttributes<Reference>) => React.ReactElement) & {
    displayName?: string;
};
export declare function genTable(shouldTriggerRender?: CompareProps<typeof Table>): ForwardGenericTable;
declare const ImmutableTable: ForwardGenericTable;
type ImmutableTableType = typeof ImmutableTable & {
    EXPAND_COLUMN: typeof EXPAND_COLUMN;
    INTERNAL_HOOKS: typeof INTERNAL_HOOKS;
    Column: typeof Column;
    ColumnGroup: typeof ColumnGroup;
    Summary: typeof FooterComponents;
};
declare const _default: ImmutableTableType;
export default _default;
