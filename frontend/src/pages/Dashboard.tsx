import React from 'react';
import { Card, Row, Col, Statistic, Typography } from 'antd';
import { UserOutlined, WechatOutlined, SyncOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  return (
    <div>
      <Title level={2}>数据概览</Title>
      
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="管理账号数"
              value={0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已登录账号"
              value={0}
              prefix={<WechatOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="今日同步次数"
              value={0}
              prefix={<SyncOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card title="最近活动" style={{ marginTop: 16 }}>
        <p>暂无数据</p>
      </Card>
    </div>
  );
};

export default Dashboard;