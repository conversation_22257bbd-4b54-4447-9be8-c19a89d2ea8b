version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
    volumes:
      - .:/app
    depends_on:
      - frontend
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - app-network

networks:
  app-network:
    driver: bridge