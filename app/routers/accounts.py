from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime
from app.database import get_db
from app.models import PlatformAccount
from app.routers.auth import get_current_user
from app.services.user_service import UserService

router = APIRouter()

# Pydantic模型
class AccountCreate(BaseModel):
    name: str
    platform: str  # wechat_mp, wechat_service, xiaohongshu

class AccountUpdate(BaseModel):
    name: str = None

class AccountResponse(BaseModel):
    id: int
    name: str
    platform: str
    login_status: bool
    last_login_time: datetime = None
    created_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[AccountResponse])
async def get_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前用户的所有平台账号"""
    accounts = UserService.get_user_accounts(db, current_user.id)
    return [AccountResponse(**account) for account in accounts]

@router.post("/", response_model=AccountResponse)
async def create_account(
    account_data: AccountCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新的平台账号"""
    # 验证平台类型
    valid_platforms = ["wechat_mp", "wechat_service", "xiaohongshu"]
    if account_data.platform not in valid_platforms:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的平台类型，支持的平台: {', '.join(valid_platforms)}"
        )
    
    # 检查同一用户是否已有相同名称的账号
    existing_account = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.name == account_data.name
    ).first()
    
    if existing_account:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="账号名称已存在"
        )
    
    # 创建新账号
    account = PlatformAccount(
        name=account_data.name,
        platform=account_data.platform,
        user_id=current_user.id,
        login_status=False
    )
    
    db.add(account)
    db.commit()
    db.refresh(account)
    
    return AccountResponse.from_orm(account)

@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定账号信息"""
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在"
        )
    
    return AccountResponse.from_orm(account)

@router.put("/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: int,
    account_update: AccountUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新账号信息"""
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在"
        )
    
    # 更新账号名称
    if account_update.name:
        # 检查新名称是否与其他账号冲突
        existing_account = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id,
            PlatformAccount.name == account_update.name,
            PlatformAccount.id != account_id
        ).first()
        
        if existing_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账号名称已存在"
            )
        
        account.name = account_update.name
    
    db.commit()
    db.refresh(account)
    
    return AccountResponse.from_orm(account)

@router.delete("/{account_id}")
async def delete_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除账号"""
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在"
        )
    
    db.delete(account)
    db.commit()
    
    return {"message": "账号删除成功"}

@router.post("/{account_id}/logout")
async def logout_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """登出账号（清除登录状态）"""
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在"
        )
    
    # 清除登录状态
    account.login_status = False
    account.cookies = None
    account.last_login_time = None
    
    db.commit()
    
    return {"message": "账号已登出"}
