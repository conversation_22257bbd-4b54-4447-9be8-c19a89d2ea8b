from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.wechat_service import WeChatMPService
from app.models import PlatformAccount
import asyncio

router = APIRouter()

# 存储活跃的登录会话
login_sessions = {}

@router.post("/login/qrcode/{account_id}")
async def get_login_qrcode(account_id: int, db: Session = Depends(get_db)):
    """获取微信公众号登录二维码"""
    
    # 查询账号
    account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    # 创建微信服务实例
    wechat_service = WeChatMPService()
    qr_code = await wechat_service.get_login_qrcode()
    
    if not qr_code:
        raise HTTPException(status_code=500, detail="获取二维码失败")
    
    # 保存会话
    login_sessions[account_id] = wechat_service
    
    return {"qrcode": qr_code, "account_id": account_id}

@router.get("/login/status/{account_id}")
async def check_login_status(account_id: int, db: Session = Depends(get_db)):
    """检查登录状态"""
    
    if account_id not in login_sessions:
        raise HTTPException(status_code=404, detail="登录会话不存在")
    
    wechat_service = login_sessions[account_id]
    is_logged_in = await wechat_service.check_login_status()
    
    if is_logged_in:
        # 获取cookies并保存
        cookies = await wechat_service.get_cookies()
        
        # 更新数据库
        account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
        account.login_status = True
        account.cookies = cookies
        account.last_login_time = datetime.utcnow()
        db.commit()
        
        # 清理会话
        await wechat_service.close()
        del login_sessions[account_id]
    
    return {"logged_in": is_logged_in}