from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from pydantic import BaseModel
from typing import Optional
from app.database import get_db
from app.services.wechat_service import WeChatMPService
from app.models import PlatformAccount, DataRecord
from app.routers.auth import get_current_user
import asyncio
import json

router = APIRouter()

# 存储活跃的登录会话
login_sessions = {}

# Pydantic模型
class DataCollectionRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    data_type: str   # user_summary, article_summary

@router.post("/login/qrcode/{account_id}")
async def get_login_qrcode(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号登录二维码"""
    
    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    # 创建微信服务实例
    wechat_service = WeChatMPService()
    qr_code = await wechat_service.get_login_qrcode()
    
    if not qr_code:
        raise HTTPException(status_code=500, detail="获取二维码失败")
    
    # 保存会话
    login_sessions[account_id] = wechat_service
    
    return {"qrcode": qr_code, "account_id": account_id}

@router.get("/login/status/{account_id}")
async def check_login_status(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查登录状态"""
    
    if account_id not in login_sessions:
        raise HTTPException(status_code=404, detail="登录会话不存在")
    
    wechat_service = login_sessions[account_id]
    is_logged_in = await wechat_service.check_login_status()
    
    if is_logged_in:
        # 获取cookies并保存
        cookies = await wechat_service.get_cookies()
        
        # 更新数据库
        account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
        account.login_status = True
        account.cookies = cookies
        account.last_login_time = datetime.utcnow()
        db.commit()
        
        # 清理会话
        await wechat_service.close()
        del login_sessions[account_id]
    
    return {"logged_in": is_logged_in}

@router.post("/collect-data/{account_id}")
async def collect_data(
    account_id: int,
    request: DataCollectionRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """采集微信公众号数据"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 检查账号是否已登录
    if not account.login_status:
        raise HTTPException(status_code=400, detail="账号未登录，请先登录")

    try:
        # 创建微信服务实例并恢复登录状态
        wechat_service = WeChatMPService()

        # 这里需要实现从cookies恢复登录状态的逻辑
        # await wechat_service.restore_login_from_cookies(account.cookies)

        collected_data = None

        if request.data_type == "user_summary":
            collected_data = await wechat_service.get_user_summary_data(
                request.start_date, request.end_date
            )
        elif request.data_type == "article_summary":
            collected_data = await wechat_service.get_article_summary_data(
                request.start_date, request.end_date
            )
        else:
            raise HTTPException(status_code=400, detail="不支持的数据类型")

        if collected_data is None:
            raise HTTPException(status_code=500, detail="数据采集失败")

        # 保存数据到数据库
        data_record = DataRecord(
            account_id=account_id,
            data_type=request.data_type,
            date=datetime.strptime(request.start_date, "%Y-%m-%d"),
            data=collected_data
        )
        db.add(data_record)
        db.commit()
        db.refresh(data_record)

        await wechat_service.close()

        return {
            "message": "数据采集成功",
            "data_record_id": data_record.id,
            "data": collected_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据采集失败: {str(e)}")

@router.get("/data/{account_id}")
async def get_collected_data(
    account_id: int,
    data_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取已采集的数据"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 构建查询条件
    query = db.query(DataRecord).filter(DataRecord.account_id == account_id)

    if data_type:
        query = query.filter(DataRecord.data_type == data_type)

    if start_date:
        query = query.filter(DataRecord.date >= datetime.strptime(start_date, "%Y-%m-%d"))

    if end_date:
        query = query.filter(DataRecord.date <= datetime.strptime(end_date, "%Y-%m-%d"))

    # 按日期降序排列
    data_records = query.order_by(DataRecord.date.desc()).all()

    return {
        "account_id": account_id,
        "account_name": account.name,
        "data_records": [
            {
                "id": record.id,
                "data_type": record.data_type,
                "date": record.date.strftime("%Y-%m-%d"),
                "data": record.data,
                "created_at": record.created_at
            }
            for record in data_records
        ]
    }