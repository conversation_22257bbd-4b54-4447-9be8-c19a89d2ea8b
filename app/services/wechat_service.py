import asyncio
import base64
from playwright.async_api import async_playwright
from typing import Optional

class WeChatMPService:
    def __init__(self):
        self.browser = None
        self.page = None
    
    async def get_login_qrcode(self) -> Optional[str]:
        """获取微信公众号登录二维码"""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=True)
            self.page = await self.browser.new_page()
            
            # 访问微信公众号登录页面
            await self.page.goto("https://mp.weixin.qq.com")
            
            # 等待二维码加载
            await self.page.wait_for_selector(".login__type__container__scan__qrcode img", timeout=10000)
            
            # 获取二维码图片
            qr_element = await self.page.query_selector(".login__type__container__scan__qrcode img")
            qr_screenshot = await qr_element.screenshot()
            
            # 转换为base64
            qr_base64 = base64.b64encode(qr_screenshot).decode()
            
            return f"data:image/png;base64,{qr_base64}"
            
        except Exception as e:
            print(f"获取二维码失败: {e}")
            return None
    
    async def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            if not self.page:
                return False
                
            # 等待页面跳转或二维码状态变化
            await asyncio.sleep(2)
            
            # 检查是否跳转到主页面
            current_url = self.page.url
            if "mp.weixin.qq.com" in current_url and "token=" in current_url:
                return True
                
            return False
            
        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False
    
    async def get_cookies(self) -> Optional[str]:
        """获取登录后的cookies"""
        try:
            if not self.page:
                return None
                
            cookies = await self.page.context.cookies()
            return str(cookies)
            
        except Exception as e:
            print(f"获取cookies失败: {e}")
            return None
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()