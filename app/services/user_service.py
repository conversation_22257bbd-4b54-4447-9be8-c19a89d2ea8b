from typing import List, Optional
from sqlalchemy.orm import Session
from app.models import User, PlatformAccount
from app.services.auth_service import AuthService

class UserService:
    """用户服务类"""
    
    @staticmethod
    def get_user_profile(db: Session, user_id: int) -> Optional[dict]:
        """获取用户资料"""
        user = AuthService.get_user_by_id(db, user_id)
        if not user:
            return None
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "created_at": user.created_at
        }
    
    @staticmethod
    def update_user_profile(db: Session, user_id: int, email: str = None) -> Optional[dict]:
        """更新用户资料"""
        user = AuthService.get_user_by_id(db, user_id)
        if not user:
            return None
        
        if email:
            # 检查邮箱是否已被其他用户使用
            existing_user = AuthService.get_user_by_email(db, email)
            if existing_user and existing_user.id != user_id:
                raise ValueError("邮箱已被其他用户使用")
            user.email = email
        
        db.commit()
        db.refresh(user)
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "created_at": user.created_at
        }
    
    @staticmethod
    def change_password(db: Session, user_id: int, old_password: str, new_password: str) -> bool:
        """修改密码"""
        user = AuthService.get_user_by_id(db, user_id)
        if not user:
            return False
        
        # 验证旧密码
        if not AuthService.verify_password(old_password, user.hashed_password):
            return False
        
        # 设置新密码
        user.hashed_password = AuthService.get_password_hash(new_password)
        db.commit()
        return True
    
    @staticmethod
    def get_user_accounts(db: Session, user_id: int) -> List[dict]:
        """获取用户的平台账号列表"""
        accounts = db.query(PlatformAccount).filter(PlatformAccount.user_id == user_id).all()
        
        return [
            {
                "id": account.id,
                "name": account.name,
                "platform": account.platform,
                "login_status": account.login_status,
                "last_login_time": account.last_login_time,
                "created_at": account.created_at
            }
            for account in accounts
        ]
    
    @staticmethod
    def delete_user(db: Session, user_id: int) -> bool:
        """删除用户（软删除或硬删除）"""
        user = AuthService.get_user_by_id(db, user_id)
        if not user:
            return False
        
        # 删除用户的所有平台账号
        db.query(PlatformAccount).filter(PlatformAccount.user_id == user_id).delete()
        
        # 删除用户
        db.delete(user)
        db.commit()
        return True
